use serde_json::Value;
use surrealdb::{engine::remote::ws::Ws, opt::auth::Root, Surreal};
use tracing::{error, info};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    println!("🔍 开始调试内置提供商查询问题");
    info!("🔍 开始调试内置提供商查询问题");

    // 连接数据库
    println!("📡 正在连接数据库...");
    let db = match Surreal::new::<Ws>("127.0.0.1:8000").await {
        Ok(db) => {
            println!("✅ 数据库连接成功");
            db
        }
        Err(e) => {
            println!("❌ 数据库连接失败: {}", e);
            return Err(e.into());
        }
    };

    // 登录
    println!("🔐 正在登录...");
    if let Err(e) = db
        .signin(Root {
            username: "root",
            password: "root",
        })
        .await
    {
        println!("❌ 登录失败: {}", e);
        return Err(e.into());
    }
    println!("✅ 登录成功");

    // 使用命名空间和数据库
    println!("🏗️ 正在选择命名空间和数据库...");
    if let Err(e) = db.use_ns("coco").use_db("main").await {
        println!("❌ 选择命名空间和数据库失败: {}", e);
        return Err(e.into());
    }
    println!("✅ 命名空间和数据库选择成功");

    info!("✅ 数据库连接成功");

    // 测试1: 直接查询所有 model_provider
    info!("🔍 测试1: 查询所有 model_provider");
    let query1 = "SELECT * FROM model_provider";
    match db.query(query1).await {
        Ok(mut response) => {
            let all_providers: Vec<Value> = response.take(0).unwrap_or_default();
            info!("📊 总共找到 {} 个提供商", all_providers.len());
            for (i, provider) in all_providers.iter().enumerate() {
                info!(
                    "  提供商 {}: {}",
                    i + 1,
                    serde_json::to_string_pretty(provider)?
                );
            }
        }
        Err(e) => {
            error!("❌ 查询所有提供商失败: {}", e);
        }
    }

    // 测试2: 查询内置提供商
    info!("🔍 测试2: 查询内置提供商 (builtin = true)");
    let query2 = "SELECT * FROM model_provider WHERE builtin = true";
    match db.query(query2).await {
        Ok(mut response) => {
            let builtin_providers: Vec<Value> = response.take(0).unwrap_or_default();
            info!("📊 找到 {} 个内置提供商", builtin_providers.len());
            for (i, provider) in builtin_providers.iter().enumerate() {
                info!(
                    "  内置提供商 {}: {}",
                    i + 1,
                    serde_json::to_string_pretty(provider)?
                );
            }
        }
        Err(e) => {
            error!("❌ 查询内置提供商失败: {}", e);
        }
    }

    // 测试3: 查询所有表
    info!("🔍 测试3: 查询所有表");
    let query3 = "INFO FOR DB";
    match db.query(query3).await {
        Ok(mut response) => {
            let db_info: Vec<Value> = response.take(0).unwrap_or_default();
            info!("📊 数据库信息:");
            for info in db_info.iter() {
                info!("  {}", serde_json::to_string_pretty(info)?);
            }
        }
        Err(e) => {
            error!("❌ 查询数据库信息失败: {}", e);
        }
    }

    // 测试3.5: 查询 builtin 字段的不同值
    info!("🔍 测试3.5: 查询 builtin 字段的不同值");
    let query35 = "SELECT builtin, count() FROM model_provider GROUP BY builtin";
    match db.query(query35).await {
        Ok(mut response) => {
            let builtin_stats: Vec<Value> = response.take(0).unwrap_or_default();
            info!("📊 builtin 字段统计:");
            for stat in builtin_stats.iter() {
                info!("  {}", serde_json::to_string_pretty(stat)?);
            }
        }
        Err(e) => {
            error!("❌ 查询 builtin 统计失败: {}", e);
        }
    }

    // 测试4: 检查数据类型
    info!("🔍 测试4: 检查 builtin 字段的数据类型");
    let query4 = "SELECT id, name, builtin, type::string(builtin) AS builtin_type FROM \
                  model_provider LIMIT 5";
    match db.query(query4).await {
        Ok(mut response) => {
            let type_check: Vec<Value> = response.take(0).unwrap_or_default();
            info!("📊 builtin 字段类型检查:");
            for item in type_check.iter() {
                info!("  {}", serde_json::to_string_pretty(item)?);
            }
        }
        Err(e) => {
            error!("❌ 类型检查失败: {}", e);
        }
    }

    // 测试5: 尝试不同的查询方式
    info!("🔍 测试5: 尝试不同的查询方式");
    let queries = vec![
        "SELECT * FROM model_provider WHERE builtin == true",
        "SELECT * FROM model_provider WHERE builtin IS true",
        "SELECT * FROM model_provider WHERE builtin = 'true'",
        "SELECT * FROM model_provider WHERE string::lowercase(string::trim(builtin)) = 'true'",
    ];

    for (i, query) in queries.iter().enumerate() {
        info!("  测试查询 {}: {}", i + 1, query);
        match db.query(*query).await {
            Ok(mut response) => {
                let results: Vec<Value> = response.take(0).unwrap_or_default();
                info!("    结果: {} 条记录", results.len());
            }
            Err(e) => {
                error!("    失败: {}", e);
            }
        }
    }

    info!("🏁 调试完成");
    Ok(())
}
